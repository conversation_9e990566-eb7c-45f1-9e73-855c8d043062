"use client";

import { useEffect, useRef, useState } from "react";
import {
  Application,
  Assets,
  Container,
  Sprite,
  Texture,
  Graphics,
  BlurFilter,
  Ticker,
} from "pixi.js";

type Phase = "idle" | "rolling" | "done";

type RollOptions = {
  targetFace?: 1 | 2 | 3 | 4 | 5 | 6;
  durationMs?: number;
  baseCycles?: number;
  jumpMs?: number;
  jumpHeight?: number;
};

export default function DicePixi3D() {
  const mountRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<Application | null>(null);
  const diceRef = useRef<Sprite | null>(null);
  const shadowRef = useRef<Graphics | null>(null);
  const [phase, setPhase] = useState<Phase>("idle");
  const [currentIdx, setCurrentIdx] = useState(0);
  const texturesRef = useRef<Texture[]>([]);
  const blurRef = useRef<BlurFilter | null>(null);

  // PIXI 앱 및 리소스 로드
  useEffect(() => {
    let unmounted = false;

    (async () => {
      const app = new Application();
      await app.init({
        width: 400,
        height: 400,
        backgroundAlpha: 0,
        antialias: true,
      });
      if (unmounted) return;
      appRef.current = app;
      mountRef.current?.appendChild(app.canvas);

      const tex: Texture[] = [];
      const assetPaths = [
        "/dice-six-faces-one.svg",
        "/dice-six-faces-two.svg",
        "/dice-six-faces-three.svg",
        "/dice-six-faces-four.svg",
        "/dice-six-faces-five.svg",
        "/dice-six-faces-six.svg",
      ];
      for (const p of assetPaths) {
        tex.push(await Assets.load(p));
      }
      if (unmounted) return;
      texturesRef.current = tex;

      const stage = new Container();
      app.stage.addChild(stage);

      // 바닥 그림자 (타원형)
      const shadow = new Graphics();
      shadow.beginFill(0x000000, 0.15);
      shadow.drawEllipse(0, 0, 90, 25);
      shadow.endFill();
      shadow.x = app.renderer.width / 2;
      shadow.y = app.renderer.height / 2 + 80;
      shadowRef.current = shadow;
      stage.addChild(shadow);

      // 주사위
      const dice = new Sprite(tex[0]);
      dice.anchor.set(0.5);
      dice.x = app.renderer.width / 2;
      dice.y = app.renderer.height / 2 - 20;
      dice.scale.set(1.2); // 기본 크기 증가
      diceRef.current = dice;

      // 블러 필터
      const blur = new BlurFilter({ strength: 0 });
      blurRef.current = blur;
      dice.filters = [blur];

      stage.addChild(dice);
    })();

    return () => {
      unmounted = true;
      try {
        appRef.current?.destroy();
      } catch {}
    };
  }, []);

  // 이징 함수들
  const easeInOutCubic = (t: number) =>
    t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  const easeOutQuint = (t: number) => 1 - Math.pow(1 - t, 5);
  const easeOutBounce = (t: number) => {
    const n1 = 7.5625;
    const d1 = 2.75;
    if (t < 1 / d1) return n1 * t * t;
    else if (t < 2 / d1) return n1 * (t -= 1.5 / d1) * t + 0.75;
    else if (t < 2.5 / d1) return n1 * (t -= 2.25 / d1) * t + 0.9375;
    else return n1 * (t -= 2.625 / d1) * t + 0.984375;
  };

  // 3D 변환 적용
  const apply3DTransform = (
    sprite: Sprite,
    rotX: number,
    rotY: number,
    rotZ: number,
    perspective: number = 0.8
  ) => {
    // 기본 회전
    sprite.rotation = rotZ;

    // Perspective skew 효과
    const skewX = Math.sin(rotY) * perspective;
    const skewY = Math.sin(rotX) * perspective * 0.5;
    sprite.skew.set(skewX, skewY);

    // 깊이감을 위한 스케일 조정
    const depthScale = 1 - Math.abs(Math.sin(rotY)) * 0.2;
    const heightScale = 1 - Math.abs(Math.sin(rotX)) * 0.15;
    sprite.scale.set(1.2 * depthScale, 1.2 * heightScale);

    // 밝기 시뮬레이션 (tint)
    const brightness = 0.7 + 0.3 * (1 - Math.abs(Math.sin(rotY)) * 0.5);
    const tintValue = Math.floor(brightness * 255);
    sprite.tint = (tintValue << 16) | (tintValue << 8) | tintValue;
  };

  // 실제 롤 실행 (3D 효과 포함)
  const roll = (opts: RollOptions = {}) => {
    if (!appRef.current || !diceRef.current || texturesRef.current.length < 6)
      return;
    if (phase === "rolling") return;

    const {
      targetFace,
      durationMs = 1200,
      baseCycles = 3,
      jumpMs = 500,
      jumpHeight = 100,
    } = opts;

    const targetIdx =
      typeof targetFace === "number"
        ? Math.max(1, Math.min(6, targetFace)) - 1
        : Math.floor(Math.random() * 6);

    const startIdx = currentIdx;
    const distance = (targetIdx - startIdx + 6) % 6;
    const totalSteps = baseCycles * 6 + distance;

    // 스텝 타이밍
    const steps: number[] = [];
    const N = totalSteps;
    for (let i = 0; i < N; i++) {
      const t = i / (N - 1 || 1);
      const w =
        t < 0.6
          ? easeInOutCubic(t / 0.6)
          : 0.6 + easeOutQuint((t - 0.6) / 0.4) * 0.4;
      steps.push(w);
    }
    const totalWeight = steps[steps.length - 1] - steps[0] || 1;
    const timeStamps = steps.map(
      (s) => ((s - steps[0]) / totalWeight) * durationMs
    );

    setPhase("rolling");

    const app = appRef.current!;
    const dice = diceRef.current!;
    const shadow = shadowRef.current!;

    const baseX = dice.x;
    const baseY = dice.y;
    const baseShadowScaleX = shadow.scale.x || 1;
    const baseShadowAlpha = shadow.alpha ?? 1;

    // 3D 회전 변수
    let rotX = 0;
    let rotY = 0;
    let rotZ = 0;
    let angularVelX = (Math.random() - 0.5) * 0.3;
    let angularVelY = (Math.random() - 0.5) * 0.3;
    let angularVelZ = (Math.random() - 0.5) * 0.2;

    // 궤적 설정
    const rollDistance = 150;
    const direction = Math.random() > 0.5 ? 1 : -1;
    const totalDuration = jumpMs + durationMs;

    let step = 0;
    let elapsedAll = 0;

    const tick = (tk: Ticker) => {
      elapsedAll += tk.elapsedMS;
      const progress = Math.min(1, elapsedAll / totalDuration);

      // 수평 이동
      const xProgress = easeOutQuint(progress);
      dice.x = baseX + direction * rollDistance * xProgress;

      // 1) 점프 단계
      if (elapsedAll <= jumpMs) {
        const t = Math.min(1, elapsedAll / jumpMs);
        const bounceHeight = easeOutBounce(t);
        dice.y = baseY - jumpHeight * bounceHeight;

        // 공중에서 빠른 3D 회전
        rotX += angularVelX;
        rotY += angularVelY;
        rotZ += angularVelZ;

        // 그림자 효과
        const currentHeight = Math.max(0, baseY - dice.y);
        const heightRatio = currentHeight / jumpHeight;
        shadow.scale.x = baseShadowScaleX * (1 - 0.5 * heightRatio);
        shadow.alpha = baseShadowAlpha * (1 - 0.7 * heightRatio);

        // 블러
        if (blurRef.current) {
          (blurRef.current as BlurFilter).strength = 2 * heightRatio;
        }

        apply3DTransform(dice, rotX, rotY, rotZ, 1.2);
        return;
      }

      // 2) 착지 후 롤링 단계
      dice.y = baseY;
      shadow.scale.x = baseShadowScaleX;
      shadow.alpha = baseShadowAlpha;

      const rollElapsed = elapsedAll - jumpMs;

      // 프레임 진행
      while (step < N && rollElapsed >= timeStamps[step]) {
        const nextIdx = (startIdx + step + 1) % 6;
        dice.texture = texturesRef.current[nextIdx];
        setCurrentIdx(nextIdx);
        step++;
      }

      const remaining = Math.max(0, N - step);
      const speedRatio = Math.min(1, Math.max(0, remaining / N));

      // 감속하는 3D 회전
      angularVelX *= 0.98;
      angularVelY *= 0.98;
      angularVelZ *= 0.98;

      rotX += angularVelX * speedRatio;
      rotY += angularVelY * speedRatio;
      rotZ += angularVelZ * speedRatio;

      // 블러/그림자
      if (blurRef.current) {
        (blurRef.current as BlurFilter).strength = 3 * speedRatio;
      }
      shadow.scale.x = baseShadowScaleX * (1 + 0.3 * speedRatio);

      apply3DTransform(dice, rotX, rotY, rotZ, 0.8 + 0.4 * speedRatio);

      // 종료
      if (step >= N && rollElapsed >= durationMs + 50) {
        app.ticker.remove(tick);
        // 최종 안정화
        dice.x = baseX;
        dice.rotation = 0;
        dice.skew.set(0, 0);
        dice.scale.set(1.2, 1.2);
        dice.tint = 0xffffff;
        if (blurRef.current) (blurRef.current as BlurFilter).strength = 0;
        shadow.scale.x = baseShadowScaleX;
        shadow.alpha = baseShadowAlpha;
        setPhase("done");
      }
    };

    app.ticker.add(tick);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div
        ref={mountRef}
        className="w-[400px] h-[400px] rounded-xl shadow border"
      />

      {phase !== "rolling" ? (
        <div className="flex gap-3 flex-wrap">
          <button
            className="px-4 py-2 rounded-lg bg-black text-white"
            onClick={() => roll()}
          >
            Roll (랜덤)
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ targetFace: 6 })}
          >
            Roll → 6
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ jumpHeight: 150, durationMs: 1500 })}
          >
            높게 Roll
          </button>
        </div>
      ) : (
        <div className="text-sm opacity-70">Rolling…</div>
      )}
    </div>
  );
}
