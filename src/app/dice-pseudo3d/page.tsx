"use client";

import dynamic from "next/dynamic";

const DicePixi3D = dynamic(() => import("./DicePixi3D"), { ssr: false });

export default function Page() {
  return (
    <div className="min-h-screen p-8 flex flex-col items-center gap-6">
      <h1 className="text-2xl font-bold">PixiJS Pseudo-3D Dice</h1>
      <p className="text-sm opacity-70">
        PixiJS + 3D 변환 효과 (skew, perspective, tint)
      </p>
      <DicePixi3D />
    </div>
  );
}
