"use client";

import dynamic from "next/dynamic";

const DicePixiRoll = dynamic(() => import("./DicePixiRoll"), { ssr: false });

export default function Page() {
  return (
    <div className="min-h-screen p-8 flex flex-col items-center gap-6">
      <h1 className="text-2xl font-bold">PixiJS Dice Roll</h1>
      <p className="text-sm opacity-70">
        가속→유지→감속, 회전/블러/스쿼시 포함
      </p>
      <DicePixiRoll />
    </div>
  );
}
