"use client";

import { useEffect, useRef, useState } from "react";
import {
  Application,
  Assets,
  Container,
  Sprite,
  Texture,
  Graphics,
  BlurFilter,
  Ticker,
} from "pixi.js";

type Phase = "idle" | "rolling" | "done";

type RollOptions = {
  targetFace?: 1 | 2 | 3 | 4 | 5 | 6; // 지정 없으면 랜덤
  durationMs?: number; // 전체 롤 시간 (기본 1200ms)
  baseCycles?: number; // 최소 몇 바퀴 도는지 (기본 3)
};

export default function DicePixiRoll() {
  const mountRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<Application | null>(null);
  const diceRef = useRef<Sprite | null>(null);
  const shadowRef = useRef<Graphics | null>(null);
  const [phase, setPhase] = useState<Phase>("idle");
  const [currentIdx, setCurrentIdx] = useState(0); // 0..5
  const texturesRef = useRef<Texture[]>([]);
  const blurRef = useRef<BlurFilter | null>(null);

  // PIXI 앱 및 리소스 로드 (Pixi v8 방식)
  useEffect(() => {
    let unmounted = false;

    (async () => {
      const app = new Application();
      await app.init({
        width: 320,
        height: 320,
        backgroundAlpha: 0,
        antialias: true,
      });
      if (unmounted) return;
      appRef.current = app;
      mountRef.current?.appendChild(app.canvas);

      const tex: Texture[] = [];
      // 현재 레포의 public 자산 이름과 매핑
      const assetPaths = [
        "/dice-six-faces-one.svg",
        "/dice-six-faces-two.svg",
        "/dice-six-faces-three.svg",
        "/dice-six-faces-four.svg",
        "/dice-six-faces-five.svg",
        "/dice-six-faces-six.svg",
      ];
      for (const p of assetPaths) {
        tex.push(await Assets.load(p));
      }
      if (unmounted) return;
      texturesRef.current = tex;

      const stage = new Container();
      app.stage.addChild(stage);

      // 바닥 그림자
      const shadow = new Graphics();
      shadow.beginFill(0x000000, 0.12);
      shadow.drawEllipse(0, 0, 70, 18);
      shadow.endFill();
      shadow.x = app.renderer.width / 2;
      shadow.y = app.renderer.height / 2 + 60;
      shadowRef.current = shadow;
      stage.addChild(shadow);

      // 주사위
      const dice = new Sprite(tex[0]);
      dice.anchor.set(0.5);
      dice.x = app.renderer.width / 2;
      dice.y = app.renderer.height / 2 - 10;
      diceRef.current = dice;

      // 블러 (속도에 비례하게 값 변경)
      const blur = new BlurFilter({ strength: 0 });
      blurRef.current = blur;
      dice.filters = [blur];

      stage.addChild(dice);
    })();

    return () => {
      unmounted = true;
      try {
        appRef.current?.destroy();
      } catch {}
    };
  }, []);

  // 이징 함수들
  const easeInOutCubic = (t: number) =>
    t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  const easeOutQuint = (t: number) => 1 - Math.pow(1 - t, 5);

  // 실제 롤 실행
  const roll = (opts: RollOptions = {}) => {
    if (!appRef.current || !diceRef.current || texturesRef.current.length < 6)
      return;
    if (phase === "rolling") return;

    const { targetFace, durationMs = 1200, baseCycles = 3 } = opts;

    // 목표 인덱스(0..5)
    const targetIdx =
      typeof targetFace === "number"
        ? Math.max(1, Math.min(6, targetFace)) - 1
        : Math.floor(Math.random() * 6);

    const startIdx = currentIdx;

    // 총 스텝 수 = 기본 바퀴(6의 배수) + 목표까지 남은 거리
    const distance = (targetIdx - startIdx + 6) % 6;
    const totalSteps = baseCycles * 6 + distance; // 예: 3바퀴 + 목표까지

    // 각 스텝의 시간 배분 (가속→감속)
    const steps: number[] = [];
    const N = totalSteps;
    for (let i = 0; i < N; i++) {
      const t = i / (N - 1 || 1);
      const w =
        t < 0.6
          ? easeInOutCubic(t / 0.6)
          : 0.6 + easeOutQuint((t - 0.6) / 0.4) * 0.4;
      steps.push(w);
    }
    const totalWeight = steps[steps.length - 1] - steps[0] || 1;
    const timeStamps = steps.map(
      (s) => ((s - steps[0]) / totalWeight) * durationMs,
    );

    setPhase("rolling");

    const app = appRef.current!;
    let step = 0;
    let elapsed = 0;

    const tick = (tk: Ticker) => {
      // Pixi v8: elapsedMS 제공
      elapsed += tk.elapsedMS;

      // 스텝 진행
      while (step < N && elapsed >= timeStamps[step]) {
        const nextIdx = (startIdx + step + 1) % 6;
        diceRef.current!.texture = texturesRef.current[nextIdx];
        setCurrentIdx(nextIdx);
        step++;
      }

      // 속도(남은 스텝) 기반 시각 효과
      const remaining = Math.max(0, N - step);
      const speedRatio = Math.min(1, Math.max(0, remaining / N)); // 1→0
      // 회전: 초반 빠르게, 후반 감속
      diceRef.current!.rotation += 0.35 * (0.4 + 0.6 * speedRatio);
      // 스쿼시/스트레치 살짝
      const s = 1 + 0.06 * Math.sin(elapsed / 40) * speedRatio;
      diceRef.current!.scale.set(s, 1 / s);
      // 블러 강도
      if (blurRef.current) {
        // Pixi v8 BlurFilter uses `strength`
        (blurRef.current as BlurFilter).strength = 3 * speedRatio;
      }
      // 그림자 스케일
      if (shadowRef.current) {
        shadowRef.current.scale.x = 1 + 0.15 * speedRatio;
      }

      // 종료
      if (step >= N && elapsed >= durationMs + 30) {
        app.ticker.remove(tick);
        // 착지 안정화
        diceRef.current!.rotation = 0;
        diceRef.current!.scale.set(1, 1);
        if (blurRef.current) (blurRef.current as BlurFilter).strength = 0;
        if (shadowRef.current) shadowRef.current.scale.x = 1;
        setPhase("done");
      }
    };

    app.ticker.add(tick);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div
        ref={mountRef}
        className="w-[320px] h-[320px] rounded-xl shadow border"
      />

      {phase !== "rolling" ? (
        <div className="flex gap-3">
          <button
            className="px-4 py-2 rounded-lg bg-black text-white"
            onClick={() => roll()} // 랜덤 착지
          >
            Roll (랜덤)
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ targetFace: 6 })} // 지정 착지
          >
            Roll → 6
          </button>
        </div>
      ) : (
        <div className="text-sm opacity-70">Rolling…</div>
      )}
    </div>
  );
}
