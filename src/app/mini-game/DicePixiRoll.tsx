"use client";

import { useEffect, useRef, useState } from "react";
import {
  Application,
  Assets,
  Container,
  Sprite,
  Texture,
  Graphics,
  BlurFilter,
  Ticker,
} from "pixi.js";

type Phase = "idle" | "rolling" | "done";

type RollOptions = {
  targetFace?: 1 | 2 | 3 | 4 | 5 | 6; // 지정 없으면 랜덤
  durationMs?: number; // 착지 이후 스핀(감속) 구간 시간 (기본 1100ms)
  baseCycles?: number; // 최소 몇 바퀴 도는지 (기본 3)
  jumpMs?: number; // 점프(상승→착지) 구간 시간 (기본 450ms)
  jumpHeight?: number; // 점프 높이(px), 기본 80
};

export default function DicePixiRoll() {
  const mountRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<Application | null>(null);
  const diceRef = useRef<Sprite | null>(null);
  const shadowRef = useRef<Graphics | null>(null);
  const [phase, setPhase] = useState<Phase>("idle");
  const [currentIdx, setCurrentIdx] = useState(0); // 0..5
  const texturesRef = useRef<Texture[]>([]);
  const blurRef = useRef<BlurFilter | null>(null);

  // PIXI 앱 및 리소스 로드 (Pixi v8 방식)
  useEffect(() => {
    let unmounted = false;

    (async () => {
      const app = new Application();
      await app.init({
        width: 320,
        height: 320,
        backgroundAlpha: 0,
        antialias: true,
      });
      if (unmounted) return;
      appRef.current = app;
      mountRef.current?.appendChild(app.canvas);

      const tex: Texture[] = [];
      // 현재 레포의 public 자산 이름과 매핑
      const assetPaths = [
        "/dice-six-faces-one.svg",
        "/dice-six-faces-two.svg",
        "/dice-six-faces-three.svg",
        "/dice-six-faces-four.svg",
        "/dice-six-faces-five.svg",
        "/dice-six-faces-six.svg",
      ];
      for (const p of assetPaths) {
        tex.push(await Assets.load(p));
      }
      if (unmounted) return;
      texturesRef.current = tex;

      const stage = new Container();
      app.stage.addChild(stage);

      // 바닥 그림자
      const shadow = new Graphics();
      shadow.beginFill(0x000000, 0.12);
      shadow.drawEllipse(0, 0, 70, 18);
      shadow.endFill();
      shadow.x = app.renderer.width / 2;
      shadow.y = app.renderer.height / 2 + 60;
      shadowRef.current = shadow;
      stage.addChild(shadow);

      // 주사위
      const dice = new Sprite(tex[0]);
      dice.anchor.set(0.5);
      dice.x = app.renderer.width / 2;
      dice.y = app.renderer.height / 2 - 10;
      diceRef.current = dice;

      // 블러 (속도에 비례하게 값 변경)
      const blur = new BlurFilter({ strength: 0 });
      blurRef.current = blur;
      dice.filters = [blur];

      stage.addChild(dice);
    })();

    return () => {
      unmounted = true;
      try {
        appRef.current?.destroy();
      } catch {}
    };
  }, []);

  // 이징 함수들
  const easeInOutCubic = (t: number) =>
    t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  const easeOutQuint = (t: number) => 1 - Math.pow(1 - t, 5);

  // 실제 롤 실행 (점프→착지→스핀/감속)
  const roll = (opts: RollOptions = {}) => {
    if (!appRef.current || !diceRef.current || texturesRef.current.length < 6)
      return;
    if (phase === "rolling") return;

    const {
      targetFace,
      durationMs = 1100, // 스핀(감속) 구간
      baseCycles = 3,
      jumpMs = 450,
      jumpHeight = 80,
    } = opts;

    // 목표 인덱스(0..5)
    const targetIdx =
      typeof targetFace === "number"
        ? Math.max(1, Math.min(6, targetFace)) - 1
        : Math.floor(Math.random() * 6);

    const startIdx = currentIdx;

    // 총 스텝 수 = 기본 바퀴(6의 배수) + 목표까지 남은 거리
    const distance = (targetIdx - startIdx + 6) % 6;
    const totalSteps = baseCycles * 6 + distance; // 예: 3바퀴 + 목표까지

    // 각 스텝의 시간 배분 (가속→감속)
    const steps: number[] = [];
    const N = totalSteps;
    for (let i = 0; i < N; i++) {
      const t = i / (N - 1 || 1);
      const w =
        t < 0.6
          ? easeInOutCubic(t / 0.6)
          : 0.6 + easeOutQuint((t - 0.6) / 0.4) * 0.4;
      steps.push(w);
    }
    const totalWeight = steps[steps.length - 1] - steps[0] || 1;
    const timeStamps = steps.map(
      (s) => ((s - steps[0]) / totalWeight) * durationMs,
    );

    setPhase("rolling");

    const app = appRef.current!;
    const dice = diceRef.current!;
    const shadow = shadowRef.current!;

    const baseY = dice.y;
    const baseShadowScaleX = shadow.scale.x || 1;
    const baseShadowAlpha = shadow.alpha ?? 1;

    let step = 0; // 스핀 스텝
    let elapsedAll = 0; // 전체 경과(ms): 점프 + 스핀

    const tick = (tk: Ticker) => {
      elapsedAll += tk.elapsedMS;

      // 1) 점프(상승→착지)
      if (elapsedAll <= jumpMs) {
        const t = Math.min(1, elapsedAll / jumpMs);
        const h = Math.sin(Math.PI * t); // 0→1→0 포물선 높이
        dice.y = baseY - jumpHeight * h;
        // 그림자: 떠오를수록 작고 옅게
        shadow.scale.x = baseShadowScaleX * (1 - 0.3 * h);
        shadow.alpha = baseShadowAlpha * (1 - 0.5 * h);
        if (blurRef.current) (blurRef.current as BlurFilter).strength = 0.6 * h;
        return; // 점프 중에는 스핀/프레임 전환 없음
      }

      // 점프 종료 보정(착지)
      dice.y = baseY;
      shadow.scale.x = baseShadowScaleX;
      shadow.alpha = baseShadowAlpha;

      // 2) 스핀/감속 구간
      const spinElapsed = elapsedAll - jumpMs;

      // 프레임 진행
      while (step < N && spinElapsed >= timeStamps[step]) {
        const nextIdx = (startIdx + step + 1) % 6;
        dice.texture = texturesRef.current[nextIdx];
        setCurrentIdx(nextIdx);
        step++;
      }

      const remaining = Math.max(0, N - step);
      const speedRatio = Math.min(1, Math.max(0, remaining / N)); // 1→0

      // 회전: 초반 빠르게, 후반 감속
      dice.rotation += 0.35 * (0.4 + 0.6 * speedRatio);

      // 착지 직후 임팩트 스쿼시 + 진행 중 약한 스쿼시
      const impact = Math.max(0, 1 - spinElapsed / 140); // 0~140ms 감쇠
      const osc = Math.sin(spinElapsed / 40) * speedRatio;
      const sx = 1 + 0.06 * osc + 0.10 * impact;
      const sy = 1 / sx;
      dice.scale.set(sx, sy);

      // 블러/그림자 보강
      if (blurRef.current)
        (blurRef.current as BlurFilter).strength = 3 * speedRatio;
      shadow.scale.x = baseShadowScaleX * (1 + 0.15 * speedRatio);

      // 종료
      if (step >= N && spinElapsed >= durationMs + 30) {
        app.ticker.remove(tick);
        dice.rotation = 0;
        dice.scale.set(1, 1);
        if (blurRef.current) (blurRef.current as BlurFilter).strength = 0;
        shadow.scale.x = baseShadowScaleX;
        shadow.alpha = baseShadowAlpha;
        setPhase("done");
      }
    };

    app.ticker.add(tick);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div
        ref={mountRef}
        className="w-[320px] h-[320px] rounded-xl shadow border"
      />

      {phase !== "rolling" ? (
        <div className="flex gap-3">
          <button
            className="px-4 py-2 rounded-lg bg-black text-white"
            onClick={() => roll()} // 랜덤 착지
          >
            Roll (랜덤)
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ targetFace: 6 })} // 지정 착지
          >
            Roll → 6
          </button>
        </div>
      ) : (
        <div className="text-sm opacity-70">Rolling…</div>
      )}
    </div>
  );
}
