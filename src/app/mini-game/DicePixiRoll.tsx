"use client";

import { useEffect, useRef, useState } from "react";
import {
  Application,
  Assets,
  Container,
  Sprite,
  Texture,
  Graphics,
  BlurFilter,
  Ticker,
} from "pixi.js";

type Phase = "idle" | "rolling" | "done";

type RollOptions = {
  targetFace?: 1 | 2 | 3 | 4 | 5 | 6; // 지정 없으면 랜덤
  durationMs?: number; // 착지 이후 스핀(감속) 구간 시간 (기본 1100ms)
  baseCycles?: number; // 최소 몇 바퀴 도는지 (기본 3)
  jumpMs?: number; // 점프(상승→착지) 구간 시간 (기본 450ms)
  jumpHeight?: number; // 점프 높이(px), 기본 80
};

export default function DicePixiRoll() {
  const mountRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<Application | null>(null);
  const diceRef = useRef<Sprite | null>(null);
  const shadowRef = useRef<Graphics | null>(null);
  const [phase, setPhase] = useState<Phase>("idle");
  const [currentIdx, setCurrentIdx] = useState(0); // 0..5
  const texturesRef = useRef<Texture[]>([]);
  const blurRef = useRef<BlurFilter | null>(null);

  // PIXI 앱 및 리소스 로드 (Pixi v8 방식)
  useEffect(() => {
    let unmounted = false;

    (async () => {
      const app = new Application();
      await app.init({
        width: 320,
        height: 320,
        backgroundAlpha: 0,
        antialias: true,
      });
      if (unmounted) return;
      appRef.current = app;
      mountRef.current?.appendChild(app.canvas);

      const tex: Texture[] = [];
      // 현재 레포의 public 자산 이름과 매핑
      const assetPaths = [
        "/dice-six-faces-one.svg",
        "/dice-six-faces-two.svg",
        "/dice-six-faces-three.svg",
        "/dice-six-faces-four.svg",
        "/dice-six-faces-five.svg",
        "/dice-six-faces-six.svg",
      ];
      for (const p of assetPaths) {
        tex.push(await Assets.load(p));
      }
      if (unmounted) return;
      texturesRef.current = tex;

      const stage = new Container();
      app.stage.addChild(stage);

      // 바닥 그림자
      const shadow = new Graphics();
      shadow.beginFill(0x000000, 0.12);
      shadow.drawEllipse(0, 0, 70, 18);
      shadow.endFill();
      shadow.x = app.renderer.width / 2;
      shadow.y = app.renderer.height / 2 + 60;
      shadowRef.current = shadow;
      stage.addChild(shadow);

      // 주사위
      const dice = new Sprite(tex[0]);
      dice.anchor.set(0.5);
      dice.x = app.renderer.width / 2;
      dice.y = app.renderer.height / 2 - 10;
      diceRef.current = dice;

      // 블러 (속도에 비례하게 값 변경)
      const blur = new BlurFilter({ strength: 0 });
      blurRef.current = blur;
      dice.filters = [blur];

      stage.addChild(dice);
    })();

    return () => {
      unmounted = true;
      try {
        appRef.current?.destroy();
      } catch {}
    };
  }, []);

  // 이징 함수들
  const easeInOutCubic = (t: number) =>
    t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  const easeOutQuint = (t: number) => 1 - Math.pow(1 - t, 5);

  // 실제 롤 실행 (굴러가는 모션: 수평이동+바운스+회전)
  const roll = (opts: RollOptions = {}) => {
    if (!appRef.current || !diceRef.current || texturesRef.current.length < 6)
      return;
    if (phase === "rolling") return;

    const {
      targetFace,
      durationMs = 1100, // 롤링 구간
      baseCycles = 3,
      jumpMs = 450,
      jumpHeight = 80,
    } = opts;

    // 목표 인덱스(0..5)
    const targetIdx =
      typeof targetFace === "number"
        ? Math.max(1, Math.min(6, targetFace)) - 1
        : Math.floor(Math.random() * 6);

    const startIdx = currentIdx;

    // 총 스텝 수 = 기본 바퀴(6의 배수) + 목표까지 남은 거리
    const distance = (targetIdx - startIdx + 6) % 6;
    const totalSteps = baseCycles * 6 + distance;

    // 각 스텝의 시간 배분 (가속→감속)
    const steps: number[] = [];
    const N = totalSteps;
    for (let i = 0; i < N; i++) {
      const t = i / (N - 1 || 1);
      const w =
        t < 0.6
          ? easeInOutCubic(t / 0.6)
          : 0.6 + easeOutQuint((t - 0.6) / 0.4) * 0.4;
      steps.push(w);
    }
    const totalWeight = steps[steps.length - 1] - steps[0] || 1;
    const timeStamps = steps.map(
      (s) => ((s - steps[0]) / totalWeight) * durationMs,
    );

    setPhase("rolling");

    const app = appRef.current!;
    const dice = diceRef.current!;
    const shadow = shadowRef.current!;

    const baseX = dice.x;
    const baseY = dice.y;
    const baseShadowScaleX = shadow.scale.x || 1;
    const baseShadowAlpha = shadow.alpha ?? 1;

    // 굴러가는 궤적 설정
    const rollDistance = 120; // 좌우 이동 거리
    const direction = Math.random() > 0.5 ? 1 : -1; // 랜덤 방향
    const totalDuration = jumpMs + durationMs;

    let step = 0;
    let elapsedAll = 0;
    let bounceCount = 0;
    const maxBounces = 3;

    const tick = (tk: Ticker) => {
      elapsedAll += tk.elapsedMS;
      const progress = Math.min(1, elapsedAll / totalDuration);

      // 수평 이동 (감속 곡선)
      const xProgress = easeOutQuint(progress);
      dice.x = baseX + direction * rollDistance * xProgress;

      // 1) 초기 점프 + 바운스들
      if (elapsedAll <= jumpMs || bounceCount < maxBounces) {
        let bounceHeight = jumpHeight;

        // 바운스 계산
        if (elapsedAll > jumpMs) {
          const bounceElapsed = elapsedAll - jumpMs;
          const bounceInterval = 200; // 각 바운스 간격
          const currentBounce = Math.floor(bounceElapsed / bounceInterval);

          if (currentBounce < maxBounces) {
            bounceCount = currentBounce + 1;
            bounceHeight = jumpHeight * Math.pow(0.6, bounceCount); // 점점 낮아짐

            const bounceProgress =
              (bounceElapsed % bounceInterval) / bounceInterval;
            const h = Math.sin(Math.PI * bounceProgress);
            dice.y = baseY - bounceHeight * h;
          } else {
            dice.y = baseY; // 바운스 종료
          }
        } else {
          // 초기 점프
          const t = Math.min(1, elapsedAll / jumpMs);
          const h = Math.sin(Math.PI * t);
          dice.y = baseY - jumpHeight * h;
        }

        // 그림자 효과
        const currentHeight = Math.max(0, baseY - dice.y);
        const heightRatio = currentHeight / jumpHeight;
        shadow.scale.x = baseShadowScaleX * (1 - 0.4 * heightRatio);
        shadow.alpha = baseShadowAlpha * (1 - 0.6 * heightRatio);

        // 공중에서 약간의 블러
        if (blurRef.current) {
          (blurRef.current as BlurFilter).strength = 1.5 * heightRatio;
        }

        // 굴러가는 회전 (X축 회전 시뮬레이션)
        const rollRotation = (elapsedAll / 100) * direction;
        dice.rotation = rollRotation;

        return;
      }

      // 2) 착지 후 롤링/감속 구간
      dice.y = baseY;
      shadow.scale.x = baseShadowScaleX;
      shadow.alpha = baseShadowAlpha;

      const rollElapsed = elapsedAll - jumpMs - maxBounces * 200;

      // 프레임 진행
      while (step < N && rollElapsed >= timeStamps[step]) {
        const nextIdx = (startIdx + step + 1) % 6;
        dice.texture = texturesRef.current[nextIdx];
        setCurrentIdx(nextIdx);
        step++;
      }

      const remaining = Math.max(0, N - step);
      const speedRatio = Math.min(1, Math.max(0, remaining / N));

      // 굴러가는 회전 (감속)
      const rollSpeed = 0.25 * (0.3 + 0.7 * speedRatio);
      dice.rotation += rollSpeed * direction;

      // 마찰 스쿼시
      const friction = Math.sin(rollElapsed / 30) * speedRatio * 0.04;
      const sx = 1 + friction;
      const sy = 1 - friction * 0.5;
      dice.scale.set(sx, sy);

      // 블러/그림자
      if (blurRef.current) {
        (blurRef.current as BlurFilter).strength = 2 * speedRatio;
      }
      shadow.scale.x = baseShadowScaleX * (1 + 0.2 * speedRatio);

      // 종료
      if (step >= N && rollElapsed >= durationMs + 30) {
        app.ticker.remove(tick);
        dice.rotation = 0;
        dice.scale.set(1, 1);
        dice.x = baseX; // 원위치
        if (blurRef.current) (blurRef.current as BlurFilter).strength = 0;
        shadow.scale.x = baseShadowScaleX;
        shadow.alpha = baseShadowAlpha;
        setPhase("done");
      }
    };

    app.ticker.add(tick);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div
        ref={mountRef}
        className="w-[320px] h-[320px] rounded-xl shadow border"
      />

      {phase !== "rolling" ? (
        <div className="flex gap-3">
          <button
            className="px-4 py-2 rounded-lg bg-black text-white"
            onClick={() => roll()} // 랜덤 착지
          >
            Roll (랜덤)
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ targetFace: 6 })} // 지정 착지
          >
            Roll → 6
          </button>
        </div>
      ) : (
        <div className="text-sm opacity-70">Rolling…</div>
      )}
    </div>
  );
}
