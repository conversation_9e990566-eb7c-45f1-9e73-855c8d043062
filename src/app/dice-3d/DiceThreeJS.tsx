"use client";

import { useEffect, useRef, useState } from "react";
import * as THREE from "three";

type Phase = "idle" | "rolling" | "done";

type RollOptions = {
  targetFace?: 1 | 2 | 3 | 4 | 5 | 6;
  duration?: number;
  force?: number;
};

export default function DiceThreeJS() {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const diceRef = useRef<THREE.Mesh | null>(null);
  const animationRef = useRef<number | null>(null);
  const [phase, setPhase] = useState<Phase>("idle");
  const [currentFace, setCurrentFace] = useState(1);

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene 설정
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // Camera 설정
    const camera = new THREE.PerspectiveCamera(75, 400 / 400, 0.1, 1000);
    camera.position.set(0, 5, 8);
    camera.lookAt(0, 0, 0);

    // Renderer 설정
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(400, 400);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;
    mountRef.current.appendChild(renderer.domElement);

    // 조명 설정
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 바닥 생성
    const floorGeometry = new THREE.PlaneGeometry(20, 20);
    const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.position.y = -2;
    floor.receiveShadow = true;
    scene.add(floor);

    // 주사위 생성
    const diceGeometry = new THREE.BoxGeometry(2, 2, 2);

    // SVG 에셋으로 텍스처 생성
    const materials = [];
    const svgPaths = [
      "/dice-six-faces-one.svg",
      "/dice-six-faces-two.svg",
      "/dice-six-faces-three.svg",
      "/dice-six-faces-four.svg",
      "/dice-six-faces-five.svg",
      "/dice-six-faces-six.svg",
    ];

    // SVG를 텍스처로 로드
    const textureLoader = new THREE.TextureLoader();
    for (const svgPath of svgPaths) {
      const texture = textureLoader.load(svgPath);
      texture.generateMipmaps = false;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      materials.push(
        new THREE.MeshLambertMaterial({
          map: texture,
          transparent: true,
        }),
      );
    }

    const dice = new THREE.Mesh(diceGeometry, materials);
    dice.position.set(0, 2, 0);
    dice.castShadow = true;
    diceRef.current = dice;
    scene.add(dice);

    // 렌더링 루프
    const animate = () => {
      animationRef.current = requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      const currentMount = mountRef.current;
      renderer.dispose();
      if (currentMount && currentMount.contains(renderer.domElement)) {
        currentMount.removeChild(renderer.domElement);
      }
    };
  }, []);

  // 면 번호에 따른 최종 회전값 계산
  const getFaceRotation = (face: number): [number, number, number] => {
    switch (face) {
      case 1:
        return [0, 0, 0]; // 앞면
      case 2:
        return [0, Math.PI, 0]; // 뒷면
      case 3:
        return [0, -Math.PI / 2, 0]; // 오른쪽
      case 4:
        return [0, Math.PI / 2, 0]; // 왼쪽
      case 5:
        return [-Math.PI / 2, 0, 0]; // 위쪽
      case 6:
        return [Math.PI / 2, 0, 0]; // 아래쪽
      default:
        return [0, 0, 0];
    }
  };

  // 3D 주사위 롤 애니메이션
  const roll = (opts: RollOptions = {}) => {
    if (!diceRef.current || phase === "rolling") return;

    const { targetFace, duration = 2000, force = 1 } = opts;
    const dice = diceRef.current;

    // 목표 면 결정
    const finalFace = targetFace || Math.floor(Math.random() * 6) + 1;
    const [finalX, finalY, finalZ] = getFaceRotation(finalFace);

    // 시작 위치 저장
    const startPos = { ...dice.position };

    // 물리 시뮬레이션 변수
    const velocity = {
      x: (Math.random() - 0.5) * 10 * force,
      y: 15 * force,
      z: (Math.random() - 0.5) * 10 * force,
    };
    const angularVelocity = {
      x: (Math.random() - 0.5) * 20 * force,
      y: (Math.random() - 0.5) * 20 * force,
      z: (Math.random() - 0.5) * 20 * force,
    };

    const gravity = -30;
    const damping = 0.98;
    const angularDamping = 0.99;
    const bounceRestitution = 0.6;

    setPhase("rolling");

    const startTime = Date.now();
    let lastTime = startTime;

    const animate = () => {
      const currentTime = Date.now();
      const deltaTime = (currentTime - lastTime) / 1000;
      const elapsed = currentTime - startTime;

      if (elapsed < duration * 0.8) {
        // 물리 시뮬레이션 단계
        velocity.y += gravity * deltaTime;

        dice.position.x += velocity.x * deltaTime;
        dice.position.y += velocity.y * deltaTime;
        dice.position.z += velocity.z * deltaTime;

        dice.rotation.x += angularVelocity.x * deltaTime;
        dice.rotation.y += angularVelocity.y * deltaTime;
        dice.rotation.z += angularVelocity.z * deltaTime;

        // 바닥 충돌
        if (dice.position.y <= 1) {
          dice.position.y = 1;
          velocity.y = -velocity.y * bounceRestitution;
          velocity.x *= damping;
          velocity.z *= damping;
          angularVelocity.x *= angularDamping;
          angularVelocity.y *= angularDamping;
          angularVelocity.z *= angularDamping;
        }

        // 공기 저항
        velocity.x *= damping;
        velocity.z *= damping;
        angularVelocity.x *= angularDamping;
        angularVelocity.y *= angularDamping;
        angularVelocity.z *= angularDamping;
      } else {
        // 안정화 단계 - 목표 면으로 부드럽게 전환
        const stabilizeProgress = Math.min(
          1,
          (elapsed - duration * 0.8) / (duration * 0.2),
        );
        const easeOut = 1 - Math.pow(1 - stabilizeProgress, 3);

        dice.position.x = THREE.MathUtils.lerp(
          dice.position.x,
          startPos.x,
          easeOut * 0.1,
        );
        dice.position.y = THREE.MathUtils.lerp(
          dice.position.y,
          startPos.y,
          easeOut * 0.1,
        );
        dice.position.z = THREE.MathUtils.lerp(
          dice.position.z,
          startPos.z,
          easeOut * 0.1,
        );

        dice.rotation.x = THREE.MathUtils.lerp(
          dice.rotation.x,
          finalX,
          easeOut * 0.2,
        );
        dice.rotation.y = THREE.MathUtils.lerp(
          dice.rotation.y,
          finalY,
          easeOut * 0.2,
        );
        dice.rotation.z = THREE.MathUtils.lerp(
          dice.rotation.z,
          finalZ,
          easeOut * 0.2,
        );
      }

      lastTime = currentTime;

      if (elapsed < duration) {
        requestAnimationFrame(animate);
      } else {
        // 최종 정리
        dice.position.copy(startPos);
        dice.rotation.set(finalX, finalY, finalZ);
        setCurrentFace(finalFace);
        setPhase("done");
      }
    };

    animate();
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div
        ref={mountRef}
        className="w-[400px] h-[400px] rounded-xl shadow border"
      />

      <div className="text-lg font-bold">현재 면: {currentFace}</div>

      {phase !== "rolling" ? (
        <div className="flex gap-3 flex-wrap">
          <button
            className="px-4 py-2 rounded-lg bg-black text-white"
            onClick={() => roll()}
          >
            Roll (랜덤)
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ targetFace: 6 })}
          >
            Roll → 6
          </button>
          <button
            className="px-3 py-2 rounded-lg border"
            onClick={() => roll({ force: 2 })}
          >
            강하게 Roll
          </button>
        </div>
      ) : (
        <div className="text-sm opacity-70">Rolling…</div>
      )}
    </div>
  );
}
